/** 菜单管理相关类型定义 */

/** 查询菜单请求参数 */
export interface IPostQueryMenuPayload {
  /** userId */
  userId: string
  /** poiId */
  poiId: string
}

/** 标准响应结构 */
export interface IResponse {
  /** 是否成功 */
  success: boolean
  /** 返回信息 */
  msg?: string
  /** code */
  code?: number
  /** // */
  data?: string
  /** // */
  bizCode?: string
}

/** 价格信息 */
export interface IPriceItem {
  /** 菜品价格 */
  dishPrice: number
  /** 价格类型 */
  priceType: number
}

/** 菜品资源信息 */
export interface IDishResourceList {
  /** 资源id */
  resourceId: string
  /** 类型 */
  type: number
  /** 资源url */
  resourceInfo: string
  /** // */
  mediaId?: string
  /** // */
  sortOrder?: number
  /** 资源url */
  resourceUrl?: string
}

/** 菜品信息 */
export interface IDishList {
  /** 菜品Id */
  dishId: string
  /** 菜品名 */
  dishName: string
  /** 价格信息 */
  priceItem: IPriceItem
  /** 资源列表 */
  dishResourceList: IDishResourceList[]
  /** 菜品状态：0-待审核，1-审核通过未生效，2-审核通过已生效，3-审核失败 */
  dishStatus?: number
  /** 是否设为招牌菜：0-否，1-是 */
  specialty: number
  /** 排序值 */
  sortOrder: number
  /** 菜品来源类型：0-商家上传，1-算法生成 */
  dishSource?: number
  /** 排序值 */
  recommendSortOrder?: number
}

/** 菜品组信息 */
export interface IMenuGroupList {
  /** 菜品组Id */
  groupId: string
  /** 菜品组名 */
  groupName: string
  /** 排序值 */
  sortOrder: number
  /** 菜品组状态：0-待审核，1-审核通过未生效，2-审核通过已生效，3-审核失败 */
  groupStatus?: number
  /** 菜品列表 */
  dishList: IDishList[]
}

/** 菜单数据 */
export interface IData {
  /** 标准响应结构体,避免直接使用这个 */
  response?: IResponse
  /** 菜品组列表 */
  menuGroupList: IMenuGroupList[]
}

/** 查询菜单响应 */
export interface IPostQueryMenuResponse {
  /** 返回值code */
  code: number
  /** 请求是否成功 */
  success: boolean
  /** 信息 */
  msg: string
  /** 数据体 */
  data: IData
}

// ==== 枚举类型定义 ====

/** 价格类型枚举 */
export enum PriceType {
  /** 固定价 */
  FIXED = 0,
  /** 起售价 */
  STARTING = 1
}

/** 菜品状态枚举 */
export enum DishStatus {
  /** 待审核 */
  PENDING = 0,
  /** 审核通过未生效 */
  APPROVED_INACTIVE = 1,
  /** 审核通过已生效 */
  APPROVED_ACTIVE = 2,
  /** 审核失败 */
  REJECTED = 3
}

/** 菜品组状态枚举 */
export enum GroupStatus {
  /** 待审核 */
  PENDING = 0,
  /** 审核通过未生效 */
  APPROVED_INACTIVE = 1,
  /** 审核通过已生效 */
  APPROVED_ACTIVE = 2,
  /** 审核失败 */
  REJECTED = 3
}

/** 是否招牌菜枚举 */
export enum SpecialtyType {
  /** 否 */
  NO = 0,
  /** 是 */
  YES = 1
}

/** 菜品来源类型枚举 */
export enum DishSourceType {
  /** 商家上传 */
  MERCHANT = 0,
  /** 算法生成 */
  ALGORITHM = 1
}

/** 资源类型枚举 */
export enum ResourceType {
  /** 图片 */
  IMAGE = 1,
  /** 视频 */
  VIDEO = 2
}

// ==== 编辑状态类型定义 ====

/** 编辑状态 */
export interface IEditState {
  /** 是否正在编辑 */
  isEditing: boolean
  /** 是否有变更 */
  hasChanges: boolean
}

// ==== 扩展类型定义 ====

/** 扩展的菜品组信息（包含招牌菜标识） */
export interface IMenuGroupListExtended extends IMenuGroupList {
  /** 是否为招牌菜分组 */
  isSpecialty?: boolean
  /** 是否为临时菜品组（未在后端创建） */
  isTemporary?: boolean
}
