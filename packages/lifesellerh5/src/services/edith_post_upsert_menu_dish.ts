/* eslint-disable */
/** !!!该文件由 XHS_API_KIT 自动生成，请不要随意手动修改，除非你十分清楚改动的影响!!!  */
/**
  * @XHS_API_KIT-INFO
  *
  * @id: 54676
  * @name: 商家上传菜单-菜品编辑/新增
  * @identifier: app.api.redlife.upsert_menu_dish.post
  * @version: undefined
  * @path: /app/api/redlife/upsert_menu_dish
  * @method: post
  * @description: 商家上传菜单-菜品编辑/新增
  *
  * @XHS_API_KIT-INFO
*/

import { http } from '@xhs/launcher'

export interface IPriceItem {
	/** 菜品价格 */
	dishPrice: number
	/** 价格类型:0:fixed(固定),1:starting(起售价) */
	priceType: number
}

export interface IDishResourceList {
	/** 资源Id */
	resourceId: string
	/** 资源类型 */
	type: number
	/** 图片来源:0:用户上传,1:平台生成 */
	resourceInfo: number
	/** 唯一Id */
	mediaId?: string
	/** 排序值 */
	sortOrder?: number
}

export interface IDish {
	/** 菜品Id */
	dishId?: string
	/** 菜品名 */
	dishName: string
	/** 菜品价格信息 */
	priceItem: IPriceItem
	/** 菜品图片列表 */
	dishResourceList?: IDishResourceList[]
	/** 菜品状态：0-待审核，1-审核通过未生效，2-审核通过已生效，3-审核失败 */
	dishStatus?: number
	/** 是否设为招牌菜：0-否，1-是 */
	specialty: number
	/** 排序值 */
	sortOrder?: number
	/** 菜品来源类型：0-商家上传，1-算法生成 */
	dishSource: number
	/** 排序值 */
	recommendSortOrder?: number
}

export interface IMenuGroup {
	/** 菜品组Id */
	groupId?: string
	/** 菜品组名 */
	groupName?: string
	/** 排序值 */
	sortOrder?: number
	/** 菜品组状态：0-待审核，1-审核通过未生效，2-审核通过已生效，3-审核失败 */
	groupStatus?: number
	/** 菜品 */
	dish: IDish
}

export interface IPostUpsertMenuDishPayload {
	/** user_id */
	userId?: string
	/** poi_id */
	poiId: string
	/** 菜品组信息 */
	menuGroup: IMenuGroup
}

export interface IResponse {
	/** 是否成功 */
	success: boolean
	/** 返回信息 */
	msg?: string
	/** code */
	code?: number
	/** // */
	data?: string
	/** // */
	bizCode?: string
}

export interface IData {
	/** 标准响应结构体,避免直接使用这个 */
	response?: IResponse
}

export interface IPostUpsertMenuDishResponse {
	/** 返回值code */
	code: number
	/** 请求是否成功 */
	success: boolean
	/** 信息 */
	msg: string
	/** 数据体 */
	data: IData
}

export function postUpsertMenuDish(payload: IPostUpsertMenuDishPayload, options = {}): Promise<IData> {
  return http.post('/app/api/redlife/upsert_menu_dish', payload, { transform: true, extractData: false, ...options,  })
}
