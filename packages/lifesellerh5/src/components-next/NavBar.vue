<template>
  <div style="z-index: 10; position: relative;">
    <NavigationBar
      :title="title"
      :safe-area="true"
      fixed
      style="background: #FFF;z-index: 10;"
      :container-style="containerStyle"
      :background-color="containerStyle?.background"
    >
      <template #left>
        <slot name="left">
          <Icon icon-name="BackCenterB" :fill="themeColor.Title" style="margin-left: 4px;" @click="goBack" />
        </slot>
      </template>
      <template #right>
        <slot name="right" />
      </template>
    </NavigationBar>
    <!-- 导航栏占位元素，确保内容不被覆盖 -->
    <div class="navbar-placeholder" :style="{ height: statusBarHeight + 44 + 'px' }"></div>
  </div>
</template>

<script setup lang="ts">
  import {
    NavigationBar, useThemeColor, Icon, useStatusBarHeight,
  } from '@xhs/reds-h5-next'
  import { invoke } from '@xhs/ozone-schema'
  import { useRouter } from 'vue-router'

  const themeColor = useThemeColor()
  const router = useRouter()

  const statusBarHeight = useStatusBarHeight()

  defineProps<{
    title: string
    containerStyle?: any
  }>()

  function goBack() {
    invoke('canGoBack').then(res => {
      if (res.value) {
        router.back()
      } else {
        invoke('closeWindow').catch(() => {
          router.back()
        })
      }
    }).catch(() => {
      router.back()
    })
  }
</script>
