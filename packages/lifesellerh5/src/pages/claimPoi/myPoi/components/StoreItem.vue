<template>
  <Loading v-if="unbindLoading" />
  <div class="store-item">
    <div class="store-info">
      <div class="store-image">
        <img v-if="store.firstImage" :src="store.firstImage" />
        <dif v-else class="pocket-bottom-img">
          <OnixIcon icon="Group2053143164" class="onix-icon-36"></OnixIcon>
          <p>待装修</p>
        </dif>
      </div>
      <div class="store-detail">
        <div class="store-name">
          <p>{{ store.poiName }}</p>
          <!-- TODO:下一期 -->
          <!-- <Tag
            size="small"
            :type="auditStatusColorMap[store.auditStatus as keyof typeof auditStatusColorMap]"
            class="mt-2"
            style="display: block; width: fit-content;"
          >
            <span>{{ auditStatusTextMap[store.auditStatus] }}</span>
          </Tag> -->
        </div>
        <div class="store-address">{{ store.addressDetail }}</div>
        <div class="store-address" @click="handleCopyStoreIdAdvanced">门店ID: {{ store.shopId }}</div>
      </div>
    </div>
    <div class="store-actions">
      <span v-if="canUnbind" class="more-btn" @click="mockClick">更多</span>
      <div class="btn-box">
        <Button
          v-if="catering"
          size="small"
          :style="{ background: 'rgba(255, 255, 255, 1)', border: '0.5px solid rgba(0, 0, 0, 0.2)', color: 'rgba(0, 0, 0, 0.8)'}"
          type="primary"
          @click="toMenuManage"
        >菜单管理</Button>
        <Button
          v-if="aptitude"
          size="small"
          :style="{ background: 'rgba(255, 255, 255, 1)', border: '0.5px solid rgba(0, 0, 0, 0.2)', color: 'rgba(0, 0, 0, 0.8)'}"
          type="primary"
          @click="addQualificationInfoRouterPush"
        >补充资质并开店</Button>
        <Button
          v-if="supplementaryShow"
          size="small"
          :style="{ background: 'rgba(255, 255, 255, 1)', border: '0.5px solid rgba(0, 0, 0, 0.2)', color: 'rgba(0, 0, 0, 0.8)'}"
          type="primary"
          @click="addQualificationInfoRouterPush"
        >补充资质</Button>
        <Button
          size="small"
          type="primary"
          variant="outline"
          @click="toDecorate"
        >装修门店</Button>
      </div>

    </div>

    <!-- 涉及开店的特殊情形提示弹窗 -->
    <Alert
      :show="showSpecialStoreOpening"
      :title="unbindTitle"
      message="是否确定并前往升级开店，开店将自动解绑当前门店，并重新进入认领和开店流程，开店后可使用更丰富的经营能力"
      footer-type="text"
      confirm-text="解绑当前门店"
      @confirm="specialStoreOpeningConfirm"
      @cancel="showSpecialStoreOpening = false"
      @close="showSpecialStoreOpening = false"
    />

    <!-- 解绑提示弹窗 -->
    <Alert
      :show="dialogConfig.show"
      :title="dialogConfig.title"
      :message="dialogConfig.message"
      footer-type="text"
      :confirm-text="dialogConfig.confirmText"
      :show-cancel-button="dialogConfig.showCancelButton"
      @confirm="handleDialogConfirm"
      @cancel="hideDialog"
      @close="hideDialog"
    />

    <!-- 解绑原因弹窗 -->
    <Sheets
      :visible="unbindReasonShow"
      :cancel="false"
      :close-type="SheetsType.SheetsActionType.icon"
      :mask="true"
      :mask-mode="SheetsType.SheetsMaskMode.dark"
      @cancel="unbindReasonCancel"
      @mask="unbindReasonCancel"
    >
      <div class="unbind-reason-body">
        <RadioGroup v-model="unbindReasonRadio" style="width: 100%" layout="verticalRight" class="radio-group">
          <Radio v-for="op of unbindReasonOptions" :key="op.text" class="radio" :name="op.name" @click.stop>{{ op.text }}</Radio>
        </RadioGroup>
        <div class="btn-box">
          <Button size="large" :style="{width: '173px', background: 'rgba(245, 245, 245, 1)', border: '0.5px solid rgba(0, 0, 0, 0.2)', color: 'rgba(0, 0, 0, 0.8)'}" type="primary" @click="unbindReasonCancel">取消</Button>
          <Button size="large" :style="{width: '173px'}" type="primary" :variant="isUnbindReasonDisabled ? 'disabled' : 'fill'" :disabled="isUnbindReasonDisabled" @click="unbindReasonConfirm">确定解绑</Button>
        </div>

      </div>
      <template #header>
        <div class="unbind-reason-header">
          <p class="title">请选择解绑门店的原因</p>
          <OnixIcon class="onix-icon-20" icon="closeB" @click="unbindReasonCancel"></OnixIcon>
        </div>
      </template>
    </Sheets>

    <Sheets
      :visible="shopSheetsVisible"
      :cancel="false"
      :close="true"
      :close-type="SheetsType.SheetsActionType.icon"
      :mask="true"
      :mask-mode="SheetsType.SheetsMaskMode.dark"
      @cancel="shopCancel"
      @confirm="shopConfirm"
    >
      <div :style="{ maxHeight: `600px`, overflow: 'scroll' }">
      </div>
      <template #header>
        <span class="sheets-header">{{ store.poiName }}</span>
      </template>
      <div class="sheets-body">
        <!-- TODO：这期没有了 -->
        <!-- <p class="sheets-body-text" @click="checkSubmittedMaterials">查看已提交材料</p> -->
        <p class="sheets-body-text" @click="onUnbindPoi">解除绑定</p>
        <div class="interval" />
        <p class="sheets-body-text border-0" @click="shopCancel">取消</p>
      </div>
    </Sheets>

  </div>
</template>

<script lang="ts" setup>

  // vue
  import {
    ref,
    computed,
    onUnmounted,
    watch,
    onMounted
  } from 'vue'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'

  // 依赖
  import {
    Button,
    Alert,
    Sheets,
    SheetsType,
    Radio,
    RadioGroup,
    // Tag
  } from '@xhs/reds-h5-next'
  import { Toast } from '@xhs/riant'
  import OnixIcon from '@xhs/onix-icon'
  import { debounce } from 'lodash'

  // 数据
  import ROUTE_NAME from '~/constants/routename'
  import {
    claimStatusEnum,
    ClaimTypeEnum,
    PoiClaimApplyType,
    AuditStatusEnum,
    QueryBusinessTypeEnum,
    UnBindStatus
  } from '~/enum/shop'
  import { unbindReasonOptions } from './content'

  // 组件
  import Loading from '~/components/loading-next/index.vue'

  // 接口
  import { postUnbindPoi, getApplyRecordStatus } from '~/services/shop'
  import { postJudgeCatering } from '~/services/edith_post_judge_catering'

  // 方法
  import getJSON from '~/utils/getJSON'
  import { copyToClipboard } from '~/utils/shopPage'

  // 类型
  import {
    IPoiClaimInfoList,
    IPostUnbindPoiPayload
  } from '~/services/shopServicesTypes'

  // 静态资源
  import '~/assets/svg/Group2053143164.svg'
  import '~/assets/svg/closeB.svg'

  const props = defineProps<{
    store: IPoiClaimInfoList
    isCreatedSeller: boolean
  }>()

  const emit = defineEmits(['refresh'])

  // 初始化vuex
  const shopStore = useStore()
  // 初始化route
  const router = useRouter()

  // ==============弹窗配置==============

  interface DialogConfig {
    show: boolean
    title: string
    message: string
    confirmText: string
    type: string
    showCancelButton: boolean
    confirmAction?: () => void
  }

  // 弹窗类型枚举
  enum DialogType {
    UNBIND_NORMAL = 'UNBIND_NORMAL',
    UNBIND_INIT = 'UNBIND_INIT',
    SPECIAL_STORE_OPENING = 'SPECIAL_STORE_OPENING',
    UNBIND_PROCESSING = 'UNBIND_PROCESSING', // 解绑处理中需要二次确认
    UNBIND_FAILED = 'UNBIND_FAILED', // 解绑失败
  // 可以添加更多类型
  }

  // 弹窗配置
  const dialogConfig = ref<DialogConfig>({
    show: false,
    title: '',
    message: '',
    confirmText: '',
    type: '',
    showCancelButton: true,
    confirmAction: undefined
  })

  // 弹窗配置预设
  const dialogPresets = {
    [DialogType.UNBIND_NORMAL]: {
      title: '解除绑定',
      message: '解绑门店将会有以下影响，请确认是否解绑：1、本门店将从门店列表移除，变为未认领状态 2、已创建商品和新发布商品不再适用于本门店 3、历史订单仍可使用当前门店账户结算和提现 4、仅本门店可用的订单将自动退款并通知用户',
      confirmText: '选择解绑原因',
      showCancelButton: true,
      confirmAction: () => {
        unbindReasonShow.value = true
        hideDialog()
      }
    },
    [DialogType.UNBIND_INIT]: {
      title: '提醒',
      message: '解除关联后，您的企业账号将不可管理此门店页',
      confirmText: '确定',
      showCancelButton: true,
      confirmAction: () => {
        hideDialog()
        confirmUnbind()
      }
    },
    [DialogType.SPECIAL_STORE_OPENING]: {
      title: '提醒',
      message: '是否确定并前往升级开店，开店将自动解绑当前门店，并重新进入认领和开店流程，开店后可使用更丰富的经营能力',
      confirmText: '解绑当前门店',
      showCancelButton: true,
      confirmAction: async () => {
        hideDialog()
        // 存储数据
        shopStore.commit('ClaimShopStore/UPDATE_PREV_ROUTE_PARAMS_STORE', { myPoiSupplementPoiInfo: props.store })

        await specialStoreOpeningConfirm()
      }
    },
    [DialogType.UNBIND_PROCESSING]: {
      title: '', // 动态设置
      message: '', // 动态设置
      confirmText: '确定',
      showCancelButton: true,
      confirmAction: () => {
        hideDialog()
      }
    },

    [DialogType.UNBIND_FAILED]: {
      title: '', // 动态设置
      message: '', // 动态设置
      confirmText: '知道了',
      confirmAction: () => {
        hideDialog()
      }
    }
  }

  // 显示弹窗的通用方法
  const showDialog = (type: DialogType, customConfig?: Partial<DialogConfig>) => {
    const preset = dialogPresets[type]
    if (!preset) return
    dialogConfig.value = {
      show: true,
      type,
      showCancelButton: true,
      ...preset,
      ...customConfig
    }
  }

  // 隐藏弹窗
  const hideDialog = () => {
    dialogConfig.value.show = false
  }

  // 处理弹窗确认
  const handleDialogConfirm = () => {
    if (dialogConfig.value.confirmAction) {
      dialogConfig.value.confirmAction()
    }
  }

  // ==============菜单管理==============
  const catering = ref(false)

  const getCatering = async () => {
    try {
      const res = await postJudgeCatering({ poiId: props.store.poiId as string })
      catering.value = res?.catering || false
    } catch (error) {
      console.error('getCatering', error)
    }
  }

  watch(() => props.store.poiId, () => {
    console.log('props.store.poiId', props.store.poiId)
    getCatering()
  })

  // ==============解绑事件==============
  // 补充资质并开店
  const aptitude = computed(() => !props.isCreatedSeller && props.store.isSupportOpenStore)

  // 补充资质
  const supplementaryShow = props.isCreatedSeller && props.store.claimStatus === claimStatusEnum.CLAIM_INIT

  const unbindLoading = ref(false)
  const claimInit = computed(() => props.store.claimStatus === claimStatusEnum.CLAIM_INIT)
  const unbindTitle = computed(() => (claimInit.value ? '提醒' : '解绑门店将会有以下影响，请确认是否解绑'))

  // 解绑原因弹窗
  const unbindReasonShow = ref(false)
  const unbindReasonRadio = ref('')
  const isUnbindReasonDisabled = computed(() => !unbindReasonRadio.value)

  // 判断是否可以解绑，只有成功和待提交才能解绑
  const canUnbind = computed(() =>
    props.store.claimStatus !== undefined
    && [claimStatusEnum.CLAIM_SUCCEEDED, claimStatusEnum.NEED_UPDATE, claimStatusEnum.CLAIM_INIT, claimStatusEnum.CLAIM_FAILED].includes(props.store.claimStatus as claimStatusEnum))

  // 点击解除绑定
  const onUnbindPoi = () => {
    shopSheetsVisible.value = false

    // 根据状态显示不同的弹窗
    const dialogType = claimInit.value ? DialogType.UNBIND_INIT : DialogType.UNBIND_NORMAL
    showDialog(dialogType)
  }

  // 解绑原因弹窗确定事件
  const unbindReasonConfirm = debounce(() => {
    unbindReasonShow.value = false
    confirmUnbind()
  }, 1000)

  // 解绑原因弹窗取消事件
  const unbindReasonCancel = () => {
    unbindReasonShow.value = false
    unbindReasonRadio.value = ''
  }

  // 解绑结果查询
  let timer
  const end = timer => {
    clearInterval(timer)
    timer = null
  }

  // 轮询解绑结果
  const circleQueryStatus = async (applyRecordId: number) => new Promise((resolve, reject) => {
    let count = 0
    timer = setInterval(async () => {
      if (count >= 5) {
        end(timer)
        Toast({ message: '解绑申请超时，请稍后再试' })
        reject(new Error('解绑申请超时'))
        return
      }

      try {
        const status = await getApplyRecordStatus({ applyRecordId })
        const handleAuditRemark: { title?: string; text?: string; type?: string } | null = getJSON(status?.auditRemark as string)

        switch (status.status) {
          case UnBindStatus.UNBINDING: // 处理中
            if (handleAuditRemark?.title && handleAuditRemark?.text && handleAuditRemark?.type === 'ORDER') {
              end(timer)
              showDialog(DialogType.UNBIND_PROCESSING, {
                title: handleAuditRemark.title,
                message: handleAuditRemark.text,
              })
              reject(new Error('解绑处理中，需要用户确认'))
            }
            return

          case UnBindStatus.UNBIND_SUCCESS:
            Toast({ message: `${props.store?.poiName}解绑成功！` })
            end(timer)
            resolve('success')
            return

          case UnBindStatus.UNBIND_FAILED:
            showDialog(DialogType.UNBIND_FAILED, {
              title: handleAuditRemark?.title,
              message: handleAuditRemark?.text,
              showCancelButton: false,
            })
            end(timer)
            reject(new Error('解绑失败'))
            return

          default: // 处理中，继续轮询
            break
        }
      } catch (error) {
        end(timer)
        reject(error)
      } finally {
        count += 1
      }
    }, 2000)
  })

  // 确认解除绑定
  const confirmUnbind = async () => {
    if (!props.store.shopId) {
      Toast({ message: '门店ID不存在' })
      throw new Error('门店ID不存在')
    }
    try {
      if (!props.store.id) {
        Toast({ message: '未查询到店铺信息，请稍后再试' })
        throw new Error('未查询到店铺信息')
      }

      unbindLoading.value = true
      const params: IPostUnbindPoiPayload = {
        reason: unbindReasonRadio.value,
        operationType: 1,
        shopIds: [props.store.shopId],
      }

      // 当前页面筛选
      if (props.store.claimStatus === claimStatusEnum.CLAIM_INIT) {
        params.sourceFrom = 'PRO_USER'
      }

      // 补充资质的判断可能会强制轻认领取消，下一期就干掉了。
      if (aptitude.value) params.sourceFrom = 'PRO_USER'

      const res = await postUnbindPoi(params)

      // 解绑接口有返回id && 不是轻认领
      if (Array.isArray(res.applyRecordIds) && res.applyRecordIds.length > 0 && props.store.claimStatus !== claimStatusEnum.CLAIM_INIT) {
        await circleQueryStatus(res.applyRecordIds[0])
      }

      // 通知父组件刷新列表
      await new Promise(resolve => {
        setTimeout(() => {
          resolve(true)
        }, 1500)
      })
      Toast({ message: '已解绑' })
      emit('refresh')
    } catch (error: any) {
      Toast({ message: error.message || '未知错误，请重试' })
      throw error
    } finally {
      unbindLoading.value = false
    }
  }

  // ==============装修门店==============
  // const auditStatusColorMap = {
  //   [AuditStatusEnum.STORE_PROCESSING]: 'info',
  //   [AuditStatusEnum.STORE_FAILED]: 'warning',
  //   [AuditStatusEnum.STORE_SUCCEEDED]: 'success'
  // }

  // const auditStatusTextMap = {
  //   [AuditStatusEnum.STORE_PROCESSING]: '装修审核中',
  //   [AuditStatusEnum.STORE_FAILED]: '装修失败',
  //   [AuditStatusEnum.STORE_SUCCEEDED]: '装修成功'
  // }

  // 装修门店
  const toDecorate = async () => {
    router.push({
      name: ROUTE_NAME.SHOP_DECORATE,
      query: {
        shopId: props.store.shopId,
        sourceFrom: props.store.claimStatus === claimStatusEnum.CLAIM_INIT ? 'PRO_USER' : '',
        fullscreen: 'true',
        isPreview: props.store.auditStatus === AuditStatusEnum.STORE_PROCESSING ? 'true' : '',
      }
    })
  }

  // 菜单管理
  const toMenuManage = async () => {
    window.open(`xhsdiscover://rn/lancer-life/course-list-preview?poiId=${props.store.poiId}`, '_blank')
  }

  // ==============补充行业资质==============
  const showSpecialStoreOpening = ref(false)
  // 修改补充资质方法
  const addQualificationInfoRouterPush = async () => {
    if (aptitude.value) {
      showDialog(DialogType.SPECIAL_STORE_OPENING)
    } else {
      shopStore.commit('ClaimShopStore/UPDATE_PREV_ROUTE_PARAMS_STORE', { myPoiSupplementPoiInfo: props.store })
      shopStore.commit('ShopUserInfoStore/UPDATE_CUSTOM_CLAIM_TYPE', ClaimTypeEnum.LOCAL_CLAIM)
      router.push({
        name: ROUTE_NAME.CLAIM_SHOP,
        query: {
          fullscreen: 'true',
          shopId: props.store.shopId,
          shopBizApplyType: PoiClaimApplyType.POI_CLAIM_APPLY,
          isSupplementQualification: 'true',
          myPoiSupplement: 'addQualificationInfo', // 说明会到认领门店，需要覆盖信息
        }
      })
    }
  }

  // 补充行业资质并开店
  const specialStoreOpeningConfirm = async () => {
    showSpecialStoreOpening.value = false

    try {
      await confirmUnbind()

      // 只有成功后才执行下面的代码
      shopStore.commit('ShopUserInfoStore/UPDATE_CUSTOM_CLAIM_TYPE', ClaimTypeEnum.MERCHANT_ENTER)
      router.push({
        name: ROUTE_NAME.CLAIM_SHOP,
        query: {
          fullscreen: 'true',
          shopId: props.store.shopId,
          isEdit: 'true',
          businessType: QueryBusinessTypeEnum.SUPPLEMENT_AND_OPEN,
          myPoiSupplement: 'specialStoreOpening', // 说明会到认领门店，需要覆盖信息
        }
      })
    } catch (error) {
      // 解绑失败，不执行后续操作
      console.error('解绑失败:', error)
    }
  }

  // ==============更多按钮事件==============
  const shopSheetsVisible = ref<boolean>(false)

  // 更多按钮事件
  const mockClick = () => {
    shopSheetsVisible.value = true
  }

  // 弹层关闭
  const shopCancel = () => {
    shopSheetsVisible.value = false
  }

  // 弹层确定
  const shopConfirm = () => {
    shopSheetsVisible.value = false
  }

  // 查看更多资料
  // const checkSubmittedMaterials = () => {
  //   router.push({
  //     name: ROUTE_NAME.BUSINESS_LICENSE_SETTLE,
  //     query: {
  //       fullscreen: 'true'
  //     }
  //   })
  // }

  // 门店id复制
  const handleCopyStoreIdAdvanced = async () => {
    if (!props.store.shopId) return

    await copyToClipboard(props.store.shopId, {
      showToast: true,
      successMessage: `门店ID已复制: ${props.store.shopId}`,
      errorMessage: '复制失败，请手动复制门店ID',
      onSuccess: () => {},
      onError: () => {}
    })
  }

  onMounted(() => {
    getCatering()
  })

  // 在组件卸载时清理定时器
  onUnmounted(() => {
    if (timer) {
      end(timer)
    }
  })

</script>

<style lang="stylus" scoped>
@import '@xhs/water-kaipingyidongBduan/index.css'

// 公共
p
  margin-block-start 0
  margin-block-end 0

.border-0
  border 0 !important

.onix-icon-36
  width 36px
  height 36px

.onix-icon-20
  width 20px
  height 20px

// 页面
.store-item
  width 100%
  padding 12px 4px 12px 12px
  border-radius 12px
  background rgba(255, 255, 255, 1)
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1)

.store-info
  display flex
  gap 12px
  padding-bottom 12px
  border-bottom 1px solid rgba(0, 0, 0, 0.08)

.store-image
  width: 68px
  height: 68px
  border-radius: 8px
  display: flex
  justify-content: start

  img
    width: 100%
    height: 100%
    object-fit: cover
    border-radius: 4px

  .image-placeholder
    width: 100%
    height: 100%
    background: rgba(0, 0, 0, 0.04)

  .pocket-bottom-img
    width: 68px;
    height: 68px;
    flex-shrink: 0;
    gap 4px
    display: flex
    flex-direction: column
    align-items center
    justify-content center
    border-radius: 8px;
    background: rgba(48, 48, 52, 0.05)

    p
      color: rgba(0, 0, 0, 0.27)
      font-size: 10px
      line-height: 14px

.store-detail
  flex: 1
  display: flex
  flex-direction: column
  gap: 4px

.store-name
  display flex
  align-items center
  justify-content space-between
  p
    width 168px
    font-size: 16px
    font-weight: 500
    color: rgba(0, 0, 0, 0.8)
    line-height: 24px
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

.store-address
  font-size: 12px
  color: rgba(0, 0, 0, 0.45)
  line-height: 18px

.qualification-tag
  display: inline-flex
  align-items: center
  gap: 2px
  padding: 3px 4px 3px 6px
  background: var(--warning2)
  border-radius: 4px
  font-size: 12px
  color: var(--warning)
  line-height: 14px
  width: fit-content

.store-actions
  display: flex
  align-items center
  justify-content space-between
  padding-right 8px
  margin-top 12px

  .more-btn
    color rgba(0, 0, 0, 0.45)
    font-size 13px
    line-height 20px

  .btn-box
    display: flex
    align-items center
    justify-content flex-end
    gap 12px
    margin-left auto

.unbind-dialog
  padding: 16px
  min-width: 270px

  .dialog-title
    font-size: 16px
    font-weight: 500
    color: var(--title)
    text-align: center
    margin-bottom: 12px

  .dialog-content
    font-size: 14px
    color: var(--description)
    margin-bottom: 16px
    line-height: 20px

// 弹窗
.sheets-header
  width 100%
  display flex
  padding 12px 16px
  justify-content center
  align-items center
  align-self stretch
  color rgba(0, 0, 0, 0.45)
  text-align center
  font-size: 12px
  line-height: 18px
  border-bottom 0.5px solid rgba(0, 0, 0, 0.08)

.sheets-body
  width 100%
  padding-bottom 10px
  background rgba(255, 255, 255, 1)
  // iOS 安全区域适配 - 底部
  @supports (padding-bottom: constant(safe-area-inset-bottom))
    padding-bottom constant(safe-area-inset-bottom)
  @supports (padding-bottom: env(safe-area-inset-bottom))
    padding-bottom env(safe-area-inset-bottom)

  .sheets-body-text
    width 100%
    padding 16px
    display flex
    justify-content center
    align-items center
    color rgba(0, 0, 0, 0.8)
    font-size 16px
    line-height 24px
    border-bottom 0.5px solid rgba(0, 0, 0, 0.08)

  .interval
    width 100%
    height 8px
    background rgba(245, 245, 245, 1)

// 解绑门店弹窗header
.unbind-reason-header
  width 100%
  height 44px
  padding 10px 12px
  display flex
  align-items center
  justify-content center
  background rgba(245, 245, 245, 1)
  border-radius 12px 12px 0 0

  .title
    flex 1
    text-align center
    color rgba(0, 0, 0, 0.8)
    font-size 16px
    font-weight 500
    line-height 24px

  .onix-icon-20
    margin-left auto  // 自动推到最右边

// 解绑门店弹窗body
.unbind-reason-body
  width 100%
  padding 12px 16px
  background rgba(245, 245, 245, 1)

  .radio-group
    width 100%
    padding 0 !important
    padding-right 16px
    background rgba(255, 255, 255, 1)
    border-radius 12px

    border-bottom 0.5px solid rgba(0, 0, 0, 0.08)

    .radio
      margin 0 !important
      padding: 0 16px
      border 0

  .btn-box
    width 100%
    margin-top 30px
    padding-bottom 2px
    gap 15.5px
    display flex
    align-items center
    justify-content space-between

  @supports (padding-bottom: env(safe-area-inset-bottom))
    padding-bottom env(safe-area-inset-bottom)
  @supports (padding-bottom: constant(safe-area-inset-bottom))
    padding-bottom constant(safe-area-inset-bottom)
</style>
