// 依赖
import type { UploadResponse, } from '@xhs/uploader'
import type { Response } from '@xhs/uploader/src/utils'
import { http } from '@xhs/launcher'

import Uploader from '@xhs/uploader'
import { showToast, ToastType } from '@xhs/reds-h5-next'
// 接口
import { postFileReplace } from '~/services/shop'

// 方法
import { getErrorMessage } from '~/utils/help'

declare type UploadModel<T> = {
  bizName: string
  scene: string
  fileId: string
  isSecret: boolean
  cloudType: number
  fileType?: string
  filename?: string
} & T

type UploadModalMapValue = UploadModel<{ url: string }>
type UploadModelWithPreview = Omit<UploadModel<{ previewUrl?: string }>, 'isSecret'>

export const DEFAULT_MAX_SIZE = 10 * 1024 * 1024

function getToken(params: { [key: string]: any }): Promise<any> {
  return http.get(
    '/api/media/v1/upload/permit',
    {
      params: {
        ...params,
        bizName: params.bizName,
        scene: params.scene,
        file_count: 1,
        version: 1,
      },
      transform: true,
    },
  )
}

// 门店装修使用的桶
const uploader = new Uploader({
  bizName: 'sale',
  scene: 'shop_menu',
  getToken,
})

function handleFile(result: Response<UploadResponse>, Body: File, maxSize?: number, permanentImageAPIType?: string): Promise<UploadModalMapValue> {
  return Promise.resolve()
    .then((): UploadModelWithPreview | Promise<UploadModelWithPreview> => {
      // 文件大小检查 - 只有当 maxSize 存在且为有效数字时才检查
      if (maxSize && typeof maxSize === 'number' && maxSize > 0) {
        // 优先使用服务器返回的文件大小，其次使用本地文件大小
        const fileSize = result?.info?.fileSize || Body.size
        const fileSizeMB = fileSize / (1024 * 1024) // 转换为 MB

        if (fileSizeMB > maxSize) {
          // 格式化文件大小显示
          const formatSize = (size: number) => {
            if (size < 1) {
              return `${(size * 1024).toFixed(0)}KB`
            }
            return `${size.toFixed(1)}MB`
          }

          const currentSizeStr = formatSize(fileSizeMB)
          const maxSizeStr = maxSize < 1 ? `${(maxSize * 1024).toFixed(0)}KB` : `${maxSize}MB`

          return Promise.reject(`图片大小不能超过 ${maxSizeStr}，当前图片大小为 ${currentSizeStr}，请选择更小的图片重新上传`)
        }
      }

      if (!result.data?.fileId) {
        return Promise.reject('上传异常，fileId 缺失')
      }

      if (result.data?.url) {
        return { ...result.data, fileType: Body.type, filename: Body.name }
      }

      // previewUrl是临时图片
      if (result.data?.previewUrl) {
        return { ...result.data, fileType: Body.type, filename: Body.name }
      }

      let detail = ''
      try {
        const temp: any = result.data || {}
        for (const i in temp) { // eslint-disable-line
          detail += `${i} : ${temp[i]}`
        }
      } catch (error) {
        console.log(error)
      }
      return Promise.reject(`上传异常  ${detail}`)
    })
    // 数据二次加工
    .then(data => ({
      ...data,
      url: data.previewUrl,
      isSecret: true,
      cloudType: 4,
    } as any))
    // 调用接口
    .then(async res => {
      const payload = {
        uploaderInfoModelList: [
          res
        ]
      }

      // 门店装修需要使用接口获取永久图片链接
      if (permanentImageAPIType === 'old') {
        try {
          const apiResult = await postFileReplace(payload)
          if (apiResult?.uploaderInfoModelList) {
            res.url = apiResult?.uploaderInfoModelList[0]?.url
          }
        } catch (error) {
          console.log(error)
        }
      }

      return res
    })
    .then(res => res)
    .catch(error => {
      showToast({
        type: ToastType.ToastBuiltInType.TEXT,
        message: getErrorMessage(error) || '上传失败请, 稍后再试',
        duration: 2000
      })
    })
}

// 门店装修使用的上传方法
export function uploadImage(Body: File, maxSize: number, permanentImageAPIType: string = 'new'): Promise<UploadModalMapValue> {
  const SliceSize = DEFAULT_MAX_SIZE
  return uploader
    .post({ Body, SliceSize })
    .then((res: any) => handleFile(res, Body, maxSize, permanentImageAPIType))
}

// 获取图片宽高
export function getImgSize(imgBase64) {
  return new Promise((resolve, reject) => {
    const image = new Image()
    image.onload = () => {
      resolve({
        width: image.width,
        height: image.height,
      })
    }
    image.onerror = () => {
      reject('图片格式错误')
    }
    image.src = imgBase64
  })
}
