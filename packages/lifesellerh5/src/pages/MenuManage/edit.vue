<template>
  <div class="menu-manage-edit">
    <ConfigProvider color-mode="platformLight">
      <MenuEditor :poi-id="poiId" />
    </ConfigProvider>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue'
  import { useRoute } from 'vue-router'
  import { ConfigProvider } from '@xhs/reds-h5-next'
  import MenuEditor from './components/MenuEditor.vue'

  const route = useRoute()

  const poiId = computed(() => route.query.poiId as string || '')
</script>
<style lang="stylus" scoped>
.menu-manage-edit
  background-color #f5f5f5
  width 100%
  height 100vh
</style>
