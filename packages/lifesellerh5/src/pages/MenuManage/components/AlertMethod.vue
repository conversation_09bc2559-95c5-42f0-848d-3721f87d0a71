<template>
  <Alert
    :title="alertState.title"
    :show="alertState.show"
    :confirm-text="alertState.confirmText"
    :cancel-text="alertState.cancelText"
    :message="alertState.message"
    :footer-layout="alertState.footerLayout"
    :footer-type="alertState.footerType"
    :show-confirm-button="alertState.showConfirmButton"
    :show-cancel-button="alertState.showCancelButton"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  />
</template>

<script setup lang="ts">
  import { ref, reactive, defineExpose } from 'vue'
  import { Alert } from '@xhs/reds-h5-next'

  // Alert 配置接口
  interface AlertConfig {
    title?: string
    message?: string
    confirmText?: string
    cancelText?: string
    footerLayout?: 'horizontal' | 'vertical'
    showConfirmButton?: boolean
    showCancelButton?: boolean
    footerType?: 'text' | 'button'
    onConfirm?: () => void | Promise<void>
    onCancel?: () => void | Promise<void>
  }

  // Alert 状态
  const alertState = reactive({
    show: false,
    title: '',
    message: '欢迎来到小红书！',
    confirmText: '确认',
    cancelText: '',
    footerLayout: 'horizontal' as 'horizontal' | 'vertical',
    showConfirmButton: true,
    showCancelButton: true,
    footerType: 'text' as 'text' | 'button',
  })

  // 存储回调函数
  const callbacks = ref<{
    onConfirm?:() => void | Promise<void>
    onCancel?:() => void | Promise<void>
  }>({})

  // 显示 Alert 的函数
  const showAlert = (config: AlertConfig) => {
    // 设置状态
    alertState.title = config.title || ''
    alertState.message = config.message || '欢迎来到小红书！'
    alertState.confirmText = config.confirmText || '确认'
    alertState.cancelText = config.cancelText || ''
    alertState.footerLayout = config.footerLayout || 'horizontal'
    alertState.showConfirmButton = config.showConfirmButton ?? true
    alertState.showCancelButton = config.showCancelButton ?? true
    alertState.footerType = config.footerType || 'text'
    // 保存回调函数
    callbacks.value.onConfirm = config.onConfirm
    callbacks.value.onCancel = config.onCancel

    // 显示弹窗
    alertState.show = true
  }

  // 快捷方法：显示提示弹窗
  const showTip = (title: string, message?: string, onConfirm?: () => void) => {
    showAlert({
      title,
      message,
      onConfirm,
    })
  }

  // 快捷方法：显示确认弹窗
  const showConfirm = (
    title: string,
    message?: string,
    onConfirm?: () => void,
    onCancel?: () => void,
    showConfirmButton?: boolean,
    showCancelButton?: boolean,
    footerLayout?: 'horizontal' | 'vertical',
    footerType?: 'text' | 'button',
  ) => {
    showAlert({
      title,
      message,
      confirmText: '确认',
      cancelText: '取消',
      footerLayout: footerLayout || 'horizontal',
      showConfirmButton: showConfirmButton || true,
      showCancelButton: showCancelButton || true,
      footerType: footerType || 'button',
      onConfirm,
      onCancel,
    })
  }

  // 处理确认事件
  const handleConfirm = async () => {
    if (callbacks.value.onConfirm) {
      await callbacks.value.onConfirm()
    }
    closeAlert()
  }

  // 处理取消事件
  const handleCancel = async () => {
    if (callbacks.value.onCancel) {
      await callbacks.value.onCancel()
    }
    closeAlert()
  }

  // 关闭 Alert
  const closeAlert = () => {
    alertState.show = false
    setTimeout(() => {
      // 重置状态
      alertState.title = ''
      alertState.message = '欢迎来到小红书！'
      alertState.confirmText = '确认'
      alertState.cancelText = ''
      alertState.footerLayout = 'horizontal'
      alertState.footerType = 'text'

      // 清空回调
      callbacks.value = {}
    }, 200)
  }

  // 暴露方法供父组件使用
  defineExpose({
    showAlert,
    showTip,
    showConfirm,
    closeAlert,
  })
</script>

<style scoped>
/* 如果需要自定义样式，可以在这里添加 */
</style>
